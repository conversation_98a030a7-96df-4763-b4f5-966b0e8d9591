﻿using System.IO.BACnet;
using HPHBACNet.Services;
using Microsoft.Extensions.Logging;
using Moq;

namespace HPHBACNet.Tests;

public class ValueConverterTests
{
    private readonly ValueConverter _converter;
    private readonly Mock<ILogger<ValueConverter>> _mockLogger;

    public ValueConverterTests()
    {
        _mockLogger = new Mock<ILogger<ValueConverter>>();
        _converter = new ValueConverter(_mockLogger.Object);
    }

    [Fact]
    public void ConvertValue_AnalogInput_Float_ReturnsDouble()
    {
        // Arrange
        var value = 25.5f;
        var objectType = "analogInput";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.IsType<double>(result);
        Assert.Equal(25.5, (double)result!, 3);
    }

    [Fact]
    public void ConvertValue_AnalogInput_WithScale_AppliesScale()
    {
        // Arrange
        var value = 100f;
        var objectType = "analogInput";
        var scale = 0.01;

        // Act
        var result = _converter.ConvertValue(value, objectType, scale);

        // Assert
        Assert.IsType<double>(result);
        Assert.Equal(1.0, (double)result!, 3);
    }

    [Fact]
    public void ConvertValue_BinaryInput_Bool_ReturnsBool()
    {
        // Arrange
        var value = true;
        var objectType = "binaryInput";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.IsType<bool>(result);
        Assert.True((bool)result!);
    }

    [Fact]
    public void ConvertValue_BinaryInput_Integer_ConvertsToBool()
    {
        // Arrange
        var value = 1;
        var objectType = "binaryInput";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.IsType<bool>(result);
        Assert.True((bool)result!);
    }

    [Fact]
    public void ConvertValue_BinaryInput_Zero_ReturnsFalse()
    {
        // Arrange
        var value = 0;
        var objectType = "binaryInput";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.IsType<bool>(result);
        Assert.False((bool)result!);
    }

    [Fact]
    public void ConvertValue_MultiStateValue_Integer_ReturnsInteger()
    {
        // Arrange
        var value = 42;
        var objectType = "multiStateValue";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.IsType<int>(result);
        Assert.Equal(42, (int)result!);
    }

    [Fact]
    public void ConvertValue_MultiStateValue_WithScale_AppliesScale()
    {
        // Arrange
        var value = 50;
        var objectType = "multiStateValue";
        var scale = 2.0;

        // Act
        var result = _converter.ConvertValue(value, objectType, scale);

        // Assert
        Assert.Equal(100.0, result);
    }

    [Fact]
    public void ConvertValue_NullValue_ReturnsNull()
    {
        // Arrange
        object? value = null;
        var objectType = "analogInput";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.Null(result);
    }

    [Theory]
    [InlineData("true", true)]
    [InlineData("false", false)]
    [InlineData("1", true)]
    [InlineData("0", false)]
    [InlineData("on", true)]
    [InlineData("off", false)]
    [InlineData("active", true)]
    [InlineData("inactive", false)]
    public void ConvertValue_BinaryInput_StringValues_ConvertsCorrectly(string input, bool expected)
    {
        // Arrange
        var objectType = "binaryInput";

        // Act
        var result = _converter.ConvertValue(input, objectType);

        // Assert
        Assert.IsType<bool>(result);
        Assert.Equal(expected, (bool)result!);
    }

    [Fact]
    public void ConvertValue_UnknownObjectType_ReturnsGenericConversion()
    {
        // Arrange
        var value = 123.45;
        var objectType = "unknownType";

        // Act
        var result = _converter.ConvertValue(value, objectType);

        // Assert
        Assert.Equal(123.45, result);
    }

    [Fact]
    public void GetValueTypeDescription_VariousTypes_ReturnsCorrectDescriptions()
    {
        // Test various types
        Assert.Equal("null", _converter.GetValueTypeDescription(null));
        Assert.Equal("boolean", _converter.GetValueTypeDescription(true));
        Assert.Equal("integer", _converter.GetValueTypeDescription(42));
        Assert.Equal("integer", _converter.GetValueTypeDescription(42L));
        Assert.Equal("number", _converter.GetValueTypeDescription(3.14));
        Assert.Equal("number", _converter.GetValueTypeDescription(3.14f));
        Assert.Equal("string", _converter.GetValueTypeDescription("test"));
        Assert.Equal("datetime", _converter.GetValueTypeDescription(DateTime.Now));
    }
}
