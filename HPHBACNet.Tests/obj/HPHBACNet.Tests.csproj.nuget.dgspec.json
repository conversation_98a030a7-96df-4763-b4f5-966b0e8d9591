{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj", "projectName": "HPHBACNet.Tests", "projectPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/HPHBACNet.csproj": {"projectPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/HPHBACNet.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BACnet": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.70, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/HPHBACNet.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/HPHBACNet.csproj", "projectName": "HPHBACNet", "projectPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/HPHBACNet.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BACnet": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}