{"version": 2, "dgSpecHash": "QZ04clQ2T5I=", "success": true, "projectFilePath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/bacnet/3.0.1/bacnet.3.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.core/5.1.1/castle.core.5.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/common.logging/3.4.1/common.logging.3.4.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/common.logging.core/3.4.1/common.logging.core.3.4.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/coverlet.collector/6.0.2/coverlet.collector.6.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/log4net/2.0.8/log4net.2.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.12.0/microsoft.codecoverage.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.0.1/microsoft.csharp.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.0/microsoft.extensions.configuration.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.0/microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.0/microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.commandline/9.0.0/microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.environmentvariables/9.0.0/microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/9.0.0/microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/9.0.0/microsoft.extensions.configuration.json.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.usersecrets/9.0.0/microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.0/microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.0/microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics/9.0.0/microsoft.extensions.diagnostics.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/9.0.0/microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/9.0.0/microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/9.0.0/microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/9.0.0/microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting/9.0.0/microsoft.extensions.hosting.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/9.0.0/microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.0/microsoft.extensions.logging.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.0/microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/9.0.0/microsoft.extensions.logging.configuration.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.console/9.0.0/microsoft.extensions.logging.console.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/9.0.0/microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventlog/9.0.0/microsoft.extensions.logging.eventlog.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventsource/9.0.0/microsoft.extensions.logging.eventsource.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.0/microsoft.extensions.options.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.0/microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.0/microsoft.extensions.primitives.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.12.0/microsoft.net.test.sdk.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/5.0.0/microsoft.netcore.platforms.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.12.0/microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.12.0/microsoft.testplatform.testhost.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.primitives/4.0.1/microsoft.win32.primitives.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/5.0.0/microsoft.win32.registry.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/moq/4.20.70/moq.4.20.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/packetdotnet/0.19.3/packetdotnet.0.19.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.linux-arm.runtime.native.system.io.ports/5.0.0/runtime.linux-arm.runtime.native.system.io.ports.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.linux-arm64.runtime.native.system.io.ports/5.0.0/runtime.linux-arm64.runtime.native.system.io.ports.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.linux-x64.runtime.native.system.io.ports/5.0.0/runtime.linux-x64.runtime.native.system.io.ports.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system/4.0.0/runtime.native.system.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.io.ports/5.0.1/runtime.native.system.io.ports.5.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.net.http/4.0.1/runtime.native.system.net.http.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography/4.0.0/runtime.native.system.security.cryptography.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx-x64.runtime.native.system.io.ports/5.0.0/runtime.osx-x64.runtime.native.system.io.ports.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharppcap/4.5.0/sharppcap.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.appcontext/4.1.0/system.appcontext.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.3.0/system.collections.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.concurrent/4.0.12/system.collections.concurrent.4.0.12.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/1.2.0/system.collections.immutable.1.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.nongeneric/4.0.1/system.collections.nongeneric.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.console/4.0.0/system.console.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.0.11/system.diagnostics.debug.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/4.0.0/system.diagnostics.diagnosticsource.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/9.0.0/system.diagnostics.eventlog.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.process/4.1.0/system.diagnostics.process.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.stacktrace/4.0.1/system.diagnostics.stacktrace.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracesource/4.0.0/system.diagnostics.tracesource.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracing/4.1.0/system.diagnostics.tracing.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.dynamic.runtime/4.0.11/system.dynamic.runtime.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.3.0/system.globalization.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.calendars/4.0.1/system.globalization.calendars.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.extensions/4.0.1/system.globalization.extensions.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem/4.0.1/system.io.filesystem.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.primitives/4.0.1/system.io.filesystem.primitives.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.watcher/4.0.0/system.io.filesystem.watcher.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.ports/5.0.1/system.io.ports.5.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.1.0/system.linq.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.expressions/4.1.0/system.linq.expressions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.http/4.1.0/system.net.http.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.nameresolution/4.0.0/system.net.nameresolution.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.primitives/4.0.11/system.net.primitives.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.requests/4.0.11/system.net.requests.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.sockets/4.1.0/system.net.sockets.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.webheadercollection/4.0.1/system.net.webheadercollection.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.objectmodel/4.0.12/system.objectmodel.4.0.12.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.0.1/system.reflection.emit.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.0.1/system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.0.1/system.reflection.emit.lightweight.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.extensions/4.0.1/system.reflection.extensions.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.typeextensions/4.1.0/system.reflection.typeextensions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.1.0/system.runtime.extensions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.handles/4.0.1/system.runtime.handles.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices/4.1.0/system.runtime.interopservices.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.runtimeinformation/4.0.0/system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.numerics/4.0.1/system.runtime.numerics.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.formatters/4.3.0/system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.primitives/4.3.0/system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/5.0.0/system.security.accesscontrol.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.algorithms/4.2.0/system.security.cryptography.algorithms.4.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/4.2.0/system.security.cryptography.cng.4.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.csp/4.0.0/system.security.cryptography.csp.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.encoding/4.0.0/system.security.cryptography.encoding.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.openssl/4.0.0/system.security.cryptography.openssl.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.primitives/4.0.0/system.security.cryptography.primitives.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.x509certificates/4.1.0/system.security.cryptography.x509certificates.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.extensions/4.0.11/system.text.encoding.extensions.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.regularexpressions/4.1.0/system.text.regularexpressions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.0.11/system.threading.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.overlapped/4.0.1/system.threading.overlapped.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.0.0/system.threading.tasks.extensions.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.thread/4.0.0/system.threading.thread.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.threadpool/4.0.10/system.threading.threadpool.4.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.timer/4.0.1/system.threading.timer.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.readerwriter/4.0.11/system.xml.readerwriter.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xmldocument/4.0.1/system.xml.xmldocument.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit/2.9.2/xunit.2.9.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.abstractions/2.0.3/xunit.abstractions.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.analyzers/1.16.0/xunit.analyzers.1.16.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.assert/2.9.2/xunit.assert.2.9.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.core/2.9.2/xunit.core.2.9.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.core/2.9.2/xunit.extensibility.core.2.9.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.execution/2.9.2/xunit.extensibility.execution.2.9.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.runner.visualstudio/2.8.2/xunit.runner.visualstudio.2.8.2.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "Error occurred while getting package vulnerability data: Access to the path '/Users/<USER>/.local/share/NuGet' is denied.", "projectPath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj", "warningLevel": 1, "filePath": "/Users/<USER>/Documents/augment-projects/HPHBACNet/HPHBACNet.Tests/HPHBACNet.Tests.csproj", "targetGraphs": []}]}