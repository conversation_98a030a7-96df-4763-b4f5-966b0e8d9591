# HPHBACNet - BACnet/IP Polling Application

A .NET 9 console application for polling BACnet/IP devices and logging data to console and JSON Lines files.

## Features

- **BACnet/IP Protocol**: Connects to BACnet gateways via UDP/47808
- **Configurable Data Points**: JSON-based configuration for data points to poll
- **Type-Safe Conversion**: Automatic conversion of BACnet values to appropriate .NET types
- **Dual Output**: Console logging with timestamps + JSON Lines files with daily rotation
- **Error Handling**: Retry logic, exponential backoff, and automatic reconnection
- **Concurrent Reads**: Configurable parallel data point reading
- **Unit Tests**: Comprehensive test coverage for data conversion layer

## Configuration

### appsettings.json
```json
{
  "Network": {
    "LocalBindAddress": null,
    "LocalPort": 47808,
    "TargetAddress": "************",
    "TargetPort": 47808
  },
  "Polling": {
    "PeriodMs": 5000,
    "TimeoutMs": 3000,
    "MaxConcurrentReads": 10,
    "MaxRetries": 2,
    "MaxBackoffSeconds": 30
  },
  "Logging": {
    "EnableConsole": true,
    "EnableFile": false,
    "FilePath": null,
    "LogLevel": "Information"
  }
}
```

### datapoints.json
```json
{
  "DataPoints": [
    {
      "Name": "MR_Summe_Abschaltungen",
      "ObjectType": "multiStateValue",
      "Instance": 20,
      "Scale": null,
      "Unit": "count"
    },
    {
      "Name": "BR01_O2_Sollwert",
      "ObjectType": "multiStateValue",
      "Instance": 112,
      "Scale": 0.01,
      "Unit": "%"
    }
  ]
}
```

## Supported BACnet Object Types

- `analogInput`, `analogOutput`, `analogValue`
- `binaryInput`, `binaryOutput`, `binaryValue`
- `multiStateInput`, `multiStateOutput`, `multiStateValue`
- `device`, `file`, `group`, `loop`
- `notificationClass`, `program`, `schedule`
- `averaging`, `trendLog`
- `lifeSafetyPoint`, `lifeSafetyZone`

## Usage

### Continuous Polling
```bash
dotnet run
```

### Single Poll (--once mode)
```bash
dotnet run -- --once
```

### Build and Run
```bash
dotnet build
dotnet run
```

### Run Tests
```bash
cd HPHBACNet.Tests
dotnet test
```

## Output

### Console Output
```
2025-08-13 18:29:21.347 ✓ MR_Summe_Abschaltungen = 0.000 count
2025-08-13 18:29:21.348 ✓ BR01_O2_Sollwert = 0.000 %
2025-08-13 18:29:21.348 ✗ Failed_Point = ERROR: Timeout
```

### JSON Lines Output (./out/bacnet-read-YYYYMMDD.ndjson)
```json
{"ts":"2025-08-13T18:29:21.347Z","name":"MR_Summe_Abschaltungen","type":"multiStateValue","instance":20,"value":0,"unit":"count","quality":"good","elapsedMs":12}
{"ts":"2025-08-13T18:29:21.348Z","name":"BR01_O2_Sollwert","type":"multiStateValue","instance":112,"value":0.0,"unit":"%","quality":"good","elapsedMs":15}
```

## Error Handling

- **Retry Logic**: Up to 2 retries per failed read with progressive delays
- **Exponential Backoff**: When all reads fail, applies exponential backoff (max 30s)
- **Auto Reconnection**: Reconnects BACnet client after 3 consecutive failures
- **Timeout Handling**: Configurable timeouts for individual read operations

## Data Conversion

The application automatically converts BACnet values to appropriate .NET types:

- **Analog Values**: Float/Double → Double (with optional scaling)
- **Binary Values**: Boolean/Integer → Boolean
- **Multi-State Values**: Integer/Enumerated → Integer (with optional scaling)
- **Bit Strings**: BacnetBitString → Integer or Boolean
- **Object IDs**: BacnetObjectId → String

## Dependencies

- **.NET 9.0**: Target framework
- **BACnet 3.0.1**: BACnet protocol library
- **Microsoft.Extensions.Hosting**: Dependency injection and hosting
- **Microsoft.Extensions.Configuration**: Configuration management
- **Microsoft.Extensions.Logging**: Logging framework

## Example Data Points (OxyControl 2.0 Gateway)

The application includes example configuration for an OxyControl 2.0 BACnet gateway with data points such as:

- `MR_Summe_Abschaltungen` (Instance 20): Total controller shutdowns
- `MR_Summe_N2_Bedarf` (Instance 68): N2 demand in m³/h
- `BR01_O2_Sollwert` (Instance 112): O2 controller setpoint in % (scaled by 0.01)

## Architecture

- **BACnetClientService**: Handles BACnet communication and connection management
- **ValueConverter**: Type-safe conversion of BACnet values to .NET types
- **OutputService**: Manages console output and JSON Lines file writing with daily rotation
- **PollingService**: Main background service with polling loop and error handling
- **Configuration Models**: Strongly-typed configuration with validation

## License

This project is provided as-is for educational and development purposes.
