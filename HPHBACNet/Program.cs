﻿using HPHBACNet.Models;
using HPHBACNet.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HPHBACNet;

class Program
{
    static async Task<int> Main(string[] args)
    {
        try
        {
            // Check for --once flag
            var runOnce = args.Contains("--once");

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddCommandLine(args)
                .Build();

            // Load data points configuration
            var dataPointsConfig = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("datapoints.json", optional: false, reloadOnChange: false)
                .Build();

            // Create host builder
            var hostBuilder = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // Configure application settings
                    services.Configure<AppConfiguration>(configuration);
                    services.Configure<DataPointConfiguration>(dataPointsConfig);

                    // Register services
                    services.AddSingleton<BACnetClientService>();
                    services.AddSingleton<OutputService>();
                    services.AddSingleton<ValueConverter>();

                    // Register polling service
                    services.AddSingleton<PollingService>(provider =>
                    {
                        var logger = provider.GetRequiredService<ILogger<PollingService>>();
                        var appConfig = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<AppConfiguration>>();
                        var dataConfig = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<DataPointConfiguration>>();
                        var bacnetClient = provider.GetRequiredService<BACnetClientService>();
                        var outputService = provider.GetRequiredService<OutputService>();
                        var valueConverter = provider.GetRequiredService<ValueConverter>();

                        return new PollingService(logger, appConfig, dataConfig, bacnetClient, outputService, valueConverter, runOnce);
                    });

                    if (!runOnce)
                    {
                        services.AddHostedService<PollingService>(provider => provider.GetRequiredService<PollingService>());
                    }
                })
                .ConfigureLogging((context, logging) =>
                {
                    logging.ClearProviders();

                    var appConfig = context.Configuration.Get<AppConfiguration>();
                    if (appConfig?.Logging.EnableConsole == true)
                    {
                        logging.AddConsole();
                    }

                    if (appConfig?.Logging.EnableFile == true && !string.IsNullOrEmpty(appConfig.Logging.FilePath))
                    {
                        // Note: For production, consider using a more robust file logging provider like Serilog
                        // For now, we'll just use console logging
                        logging.AddConsole();
                    }

                    // Set log level
                    if (Enum.TryParse<LogLevel>(appConfig?.Logging.LogLevel ?? "Information", out var logLevel))
                    {
                        logging.SetMinimumLevel(logLevel);
                    }
                });

            // Build and run host
            using var host = hostBuilder.Build();

            if (runOnce)
            {
                // Run once mode
                var pollingService = host.Services.GetRequiredService<PollingService>();
                await pollingService.StartAsync(CancellationToken.None);

                // Wait for the service to complete its single execution
                var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5)); // 5 minute timeout
                try
                {
                    await Task.Delay(100, cts.Token); // Give it time to start
                    while (!cts.Token.IsCancellationRequested)
                    {
                        await Task.Delay(100, cts.Token);
                        // In a real implementation, you'd check if the service completed
                        break; // For now, just break after first iteration
                    }
                }
                catch (OperationCanceledException)
                {
                    // Timeout occurred
                }

                await pollingService.StopAsync(CancellationToken.None);
            }
            else
            {
                // Continuous mode
                await host.RunAsync();
            }

            return 0;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Fatal error: {ex.Message}");
            Console.Error.WriteLine(ex.StackTrace);
            return 1;
        }
    }
}
