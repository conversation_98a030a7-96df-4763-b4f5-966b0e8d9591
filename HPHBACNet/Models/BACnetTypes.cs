using System.IO.BACnet;

namespace HPHBACNet.Models;

/// <summary>
/// BACnet object type enumeration mapping
/// </summary>
public static class BACnetObjectTypes
{
    private static readonly Dictionary<string, BacnetObjectTypes> _typeMap = new(StringComparer.OrdinalIgnoreCase)
    {
        { "analogInput", BacnetObjectTypes.OBJECT_ANALOG_INPUT },
        { "analogOutput", BacnetObjectTypes.OBJECT_ANALOG_OUTPUT },
        { "analogValue", BacnetObjectTypes.OBJECT_ANALOG_VALUE },
        { "binaryInput", BacnetObjectTypes.OBJECT_BINARY_INPUT },
        { "binaryOutput", BacnetObjectTypes.OBJECT_BINARY_OUTPUT },
        { "binaryValue", BacnetObjectTypes.OBJECT_BINARY_VALUE },
        { "multiStateInput", BacnetObjectTypes.OBJECT_MULTI_STATE_INPUT },
        { "multiStateOutput", BacnetObjectTypes.OBJECT_MULTI_STATE_OUTPUT },
        { "multiStateValue", BacnetObjectTypes.OBJECT_MULTI_STATE_VALUE },
        { "device", BacnetObjectTypes.OBJECT_DEVICE },
        { "file", BacnetObjectTypes.OBJECT_FILE },
        { "group", BacnetObjectTypes.OBJECT_GROUP },
        { "loop", BacnetObjectTypes.OBJECT_LOOP },
        { "notificationClass", BacnetObjectTypes.OBJECT_NOTIFICATION_CLASS },
        { "program", BacnetObjectTypes.OBJECT_PROGRAM },
        { "schedule", BacnetObjectTypes.OBJECT_SCHEDULE },
        { "averaging", BacnetObjectTypes.OBJECT_AVERAGING },
        { "trendLog", BacnetObjectTypes.OBJECT_TRENDLOG },
        { "lifeSafetyPoint", BacnetObjectTypes.OBJECT_LIFE_SAFETY_POINT },
        { "lifeSafetyZone", BacnetObjectTypes.OBJECT_LIFE_SAFETY_ZONE }
    };

    public static BacnetObjectTypes Parse(string objectType)
    {
        if (_typeMap.TryGetValue(objectType, out var bacnetType))
        {
            return bacnetType;
        }
        
        throw new ArgumentException($"Unknown BACnet object type: {objectType}", nameof(objectType));
    }

    public static bool TryParse(string objectType, out BacnetObjectTypes bacnetType)
    {
        return _typeMap.TryGetValue(objectType, out bacnetType);
    }
}

/// <summary>
/// Result of a BACnet read operation
/// </summary>
public record BACnetReadResult
{
    /// <summary>
    /// Data point name
    /// </summary>
    public required string Name { get; init; }
    
    /// <summary>
    /// BACnet object type
    /// </summary>
    public required string ObjectType { get; init; }
    
    /// <summary>
    /// BACnet object instance
    /// </summary>
    public required int Instance { get; init; }
    
    /// <summary>
    /// Read value (converted to appropriate .NET type)
    /// </summary>
    public object? Value { get; init; }
    
    /// <summary>
    /// Unit description
    /// </summary>
    public string? Unit { get; init; }
    
    /// <summary>
    /// Data quality ("good", "bad", "uncertain")
    /// </summary>
    public required string Quality { get; init; }
    
    /// <summary>
    /// Timestamp of the read operation
    /// </summary>
    public required DateTimeOffset Timestamp { get; init; }
    
    /// <summary>
    /// Time taken for the read operation in milliseconds
    /// </summary>
    public required long ElapsedMs { get; init; }
    
    /// <summary>
    /// Error message if read failed
    /// </summary>
    public string? Error { get; init; }
}

/// <summary>
/// JSON output record for NDJSON file
/// </summary>
public record JsonOutputRecord
{
    /// <summary>
    /// ISO 8601 timestamp
    /// </summary>
    public required string Ts { get; init; }
    
    /// <summary>
    /// Data point name
    /// </summary>
    public required string Name { get; init; }
    
    /// <summary>
    /// BACnet object type
    /// </summary>
    public required string Type { get; init; }
    
    /// <summary>
    /// BACnet object instance
    /// </summary>
    public required int Instance { get; init; }
    
    /// <summary>
    /// Read value
    /// </summary>
    public object? Value { get; init; }
    
    /// <summary>
    /// Unit description
    /// </summary>
    public string? Unit { get; init; }
    
    /// <summary>
    /// Data quality
    /// </summary>
    public required string Quality { get; init; }
    
    /// <summary>
    /// Elapsed time in milliseconds
    /// </summary>
    public required long ElapsedMs { get; init; }
}
