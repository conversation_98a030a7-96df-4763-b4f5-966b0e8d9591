using System.ComponentModel.DataAnnotations;

namespace HPHBACNet.Models;

/// <summary>
/// Main application configuration from appsettings.json
/// </summary>
public record AppConfiguration
{
    public NetworkConfiguration Network { get; init; } = new() { TargetAddress = "127.0.0.1" };
    public PollingConfiguration Polling { get; init; } = new();
    public LoggingConfiguration Logging { get; init; } = new();
}

/// <summary>
/// Network configuration for BACnet/IP communication
/// </summary>
public record NetworkConfiguration
{
    /// <summary>
    /// Local IP address to bind to (null for any)
    /// </summary>
    public string? LocalBindAddress { get; init; }
    
    /// <summary>
    /// Local port to bind to (default: 47808)
    /// </summary>
    [Range(1, 65535)]
    public int LocalPort { get; init; } = 47808;
    
    /// <summary>
    /// Target BACnet gateway IP address
    /// </summary>
    [Required]
    public required string TargetAddress { get; init; }
    
    /// <summary>
    /// Target BACnet gateway port (default: 47808)
    /// </summary>
    [Range(1, 65535)]
    public int TargetPort { get; init; } = 47808;
}

/// <summary>
/// Polling configuration
/// </summary>
public record PollingConfiguration
{
    /// <summary>
    /// Polling period in milliseconds
    /// </summary>
    [Range(100, 300000)]
    public int PeriodMs { get; init; } = 5000;
    
    /// <summary>
    /// Timeout for individual read operations in milliseconds
    /// </summary>
    [Range(100, 30000)]
    public int TimeoutMs { get; init; } = 3000;
    
    /// <summary>
    /// Maximum number of concurrent read operations
    /// </summary>
    [Range(1, 50)]
    public int MaxConcurrentReads { get; init; } = 10;
    
    /// <summary>
    /// Maximum number of retry attempts for failed reads
    /// </summary>
    [Range(0, 5)]
    public int MaxRetries { get; init; } = 2;
    
    /// <summary>
    /// Maximum backoff time in seconds when all reads fail
    /// </summary>
    [Range(1, 300)]
    public int MaxBackoffSeconds { get; init; } = 30;
}

/// <summary>
/// Logging configuration
/// </summary>
public record LoggingConfiguration
{
    /// <summary>
    /// Enable console logging
    /// </summary>
    public bool EnableConsole { get; init; } = true;
    
    /// <summary>
    /// Enable file logging
    /// </summary>
    public bool EnableFile { get; init; } = false;
    
    /// <summary>
    /// Log file path (optional)
    /// </summary>
    public string? FilePath { get; init; }
    
    /// <summary>
    /// Minimum log level
    /// </summary>
    public string LogLevel { get; init; } = "Information";
}

/// <summary>
/// Data point configuration from datapoints.json
/// </summary>
public record DataPointConfiguration
{
    /// <summary>
    /// List of data points to poll
    /// </summary>
    public List<DataPoint> DataPoints { get; init; } = new();
}

/// <summary>
/// Individual data point definition
/// </summary>
public record DataPoint
{
    /// <summary>
    /// Unique name for the data point
    /// </summary>
    [Required]
    public required string Name { get; init; }
    
    /// <summary>
    /// BACnet object type (e.g., "analogInput", "multiStateValue", "binaryOutput")
    /// </summary>
    [Required]
    public required string ObjectType { get; init; }
    
    /// <summary>
    /// BACnet object instance number
    /// </summary>
    [Range(0, int.MaxValue)]
    public required int Instance { get; init; }
    
    /// <summary>
    /// Optional scaling factor for numeric values
    /// </summary>
    public double? Scale { get; init; }
    
    /// <summary>
    /// Optional unit description
    /// </summary>
    public string? Unit { get; init; }
}
