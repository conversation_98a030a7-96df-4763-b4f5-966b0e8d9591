using System.Diagnostics;
using System.IO.BACnet;
using System.Net;
using HPHBACNet.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace HPHBACNet.Services;

/// <summary>
/// BACnet/IP client service for reading data points
/// </summary>
public class BACnetClientService : IDisposable
{
    private readonly ILogger<BACnetClientService> _logger;
    private readonly AppConfiguration _config;
    private BacnetClient? _client;
    private BacnetAddress? _targetAddress;
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private bool _disposed;

    public BACnetClientService(ILogger<BACnetClientService> logger, IOptions<AppConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
    }

    /// <summary>
    /// Initialize the BACnet client and establish connection
    /// </summary>
    public async Task<bool> InitializeAsync(CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_client != null)
            {
                return true; // Already initialized
            }

            _logger.LogInformation("Initializing BACnet client...");

            // Create BACnet transport
            var transport = new BacnetIpUdpProtocolTransport(_config.Network.LocalPort, false);
            
            // Bind to specific local address if specified
            if (!string.IsNullOrEmpty(_config.Network.LocalBindAddress))
            {
                if (IPAddress.TryParse(_config.Network.LocalBindAddress, out var localIp))
                {
                    // Create transport with specific local IP
                    transport = new BacnetIpUdpProtocolTransport(_config.Network.LocalPort, false);
                    _logger.LogInformation("Binding to local address: {LocalAddress}:{LocalPort}",
                        _config.Network.LocalBindAddress, _config.Network.LocalPort);
                }
                else
                {
                    _logger.LogWarning("Invalid local bind address: {LocalAddress}, using any address",
                        _config.Network.LocalBindAddress);
                }
            }

            // Create BACnet client
            _client = new BacnetClient(transport);
            
            // Start the client
            _client.Start();
            
            // Create target address
            if (!IPAddress.TryParse(_config.Network.TargetAddress, out var targetIp))
            {
                _logger.LogError("Invalid target IP address: {TargetAddress}", _config.Network.TargetAddress);
                return false;
            }

            _targetAddress = new BacnetAddress(BacnetAddressTypes.IP, 
                new IPEndPoint(targetIp, _config.Network.TargetPort).ToString());

            _logger.LogInformation("BACnet client initialized successfully. Target: {TargetAddress}:{TargetPort}", 
                _config.Network.TargetAddress, _config.Network.TargetPort);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize BACnet client");
            return false;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Read multiple data points concurrently
    /// </summary>
    public async Task<List<BACnetReadResult>> ReadDataPointsAsync(
        IEnumerable<DataPoint> dataPoints, 
        CancellationToken cancellationToken = default)
    {
        if (_client == null || _targetAddress == null)
        {
            throw new InvalidOperationException("BACnet client not initialized. Call InitializeAsync first.");
        }

        var results = new List<BACnetReadResult>();
        var semaphore = new SemaphoreSlim(_config.Polling.MaxConcurrentReads, _config.Polling.MaxConcurrentReads);
        
        var tasks = dataPoints.Select(async dataPoint =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                return await ReadSingleDataPointAsync(dataPoint, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        var taskResults = await Task.WhenAll(tasks);
        results.AddRange(taskResults);

        return results;
    }

    /// <summary>
    /// Read a single data point with retry logic
    /// </summary>
    private async Task<BACnetReadResult> ReadSingleDataPointAsync(DataPoint dataPoint, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var timestamp = DateTimeOffset.UtcNow;

        for (int attempt = 0; attempt <= _config.Polling.MaxRetries; attempt++)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, "Operation cancelled");
                }

                // Parse BACnet object type
                if (!BACnetObjectTypes.TryParse(dataPoint.ObjectType, out var objectType))
                {
                    return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, 
                        $"Invalid object type: {dataPoint.ObjectType}");
                }

                // Create object identifier
                var objectId = new BacnetObjectId(objectType, (uint)dataPoint.Instance);

                // Read present value property
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(_config.Polling.TimeoutMs);

                var readTask = Task.Run(() =>
                {
                    var success = _client!.ReadPropertyRequest(_targetAddress, objectId,
                        BacnetPropertyIds.PROP_PRESENT_VALUE, out var values);

                    return success ? values : null;
                }, timeoutCts.Token);

                var values = await readTask;

                if (values != null && values.Count > 0)
                {
                    var convertedValue = ConvertBACnetValue(values[0].Value, dataPoint.Scale);
                    
                    return new BACnetReadResult
                    {
                        Name = dataPoint.Name,
                        ObjectType = dataPoint.ObjectType,
                        Instance = dataPoint.Instance,
                        Value = convertedValue,
                        Unit = dataPoint.Unit,
                        Quality = "good",
                        Timestamp = timestamp,
                        ElapsedMs = stopwatch.ElapsedMilliseconds
                    };
                }
                else
                {
                    if (attempt < _config.Polling.MaxRetries)
                    {
                        _logger.LogWarning("Read failed for {DataPoint}, attempt {Attempt}/{MaxRetries}", 
                            dataPoint.Name, attempt + 1, _config.Polling.MaxRetries + 1);
                        await Task.Delay(100 * (attempt + 1), cancellationToken); // Progressive delay
                        continue;
                    }
                    
                    return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, "No data returned");
                }
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, "Operation cancelled");
            }
            catch (Exception ex)
            {
                if (attempt < _config.Polling.MaxRetries)
                {
                    _logger.LogWarning(ex, "Read failed for {DataPoint}, attempt {Attempt}/{MaxRetries}", 
                        dataPoint.Name, attempt + 1, _config.Polling.MaxRetries + 1);
                    await Task.Delay(100 * (attempt + 1), cancellationToken); // Progressive delay
                    continue;
                }
                
                _logger.LogError(ex, "Failed to read data point {DataPoint} after {MaxRetries} attempts", 
                    dataPoint.Name, _config.Polling.MaxRetries + 1);
                
                return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, ex.Message);
            }
        }

        return CreateErrorResult(dataPoint, timestamp, stopwatch.ElapsedMilliseconds, "Max retries exceeded");
    }

    /// <summary>
    /// Convert BACnet value to appropriate .NET type
    /// </summary>
    private static object? ConvertBACnetValue(object? bacnetValue, double? scale)
    {
        if (bacnetValue == null)
            return null;

        return bacnetValue switch
        {
            bool boolValue => boolValue,
            float floatValue => scale.HasValue ? floatValue * scale.Value : (double)floatValue,
            double doubleValue => scale.HasValue ? doubleValue * scale.Value : doubleValue,
            int intValue => scale.HasValue ? intValue * scale.Value : intValue,
            uint uintValue => scale.HasValue ? uintValue * scale.Value : (long)uintValue,
            BacnetBitString bitString => ConvertBitString(bitString),
            BacnetObjectId objectId => objectId.ToString(),
            _ => bacnetValue.ToString()
        };
    }

    /// <summary>
    /// Convert BACnet bit string to integer
    /// </summary>
    private static int ConvertBitString(BacnetBitString bitString)
    {
        try
        {
            // Try to convert bit string to integer representation
            return bitString.GetHashCode(); // Fallback approach
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// Create an error result for a failed read operation
    /// </summary>
    private static BACnetReadResult CreateErrorResult(DataPoint dataPoint, DateTimeOffset timestamp, long elapsedMs, string error)
    {
        return new BACnetReadResult
        {
            Name = dataPoint.Name,
            ObjectType = dataPoint.ObjectType,
            Instance = dataPoint.Instance,
            Value = null,
            Unit = dataPoint.Unit,
            Quality = "bad",
            Timestamp = timestamp,
            ElapsedMs = elapsedMs,
            Error = error
        };
    }

    /// <summary>
    /// Reconnect the BACnet client
    /// </summary>
    public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Reconnecting BACnet client...");
            
            // Dispose existing client
            _client?.Dispose();
            _client = null;
            _targetAddress = null;

            // Reinitialize
            return await InitializeAsync(cancellationToken);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _client?.Dispose();
            _connectionSemaphore.Dispose();
            _disposed = true;
        }
    }
}
