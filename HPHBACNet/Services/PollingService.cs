using HPHBACNet.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace HPHBACNet.Services;

/// <summary>
/// Background service that handles BACnet polling with error handling and backoff strategy
/// </summary>
public class PollingService : BackgroundService
{
    private readonly ILogger<PollingService> _logger;
    private readonly AppConfiguration _appConfig;
    private readonly DataPointConfiguration _dataConfig;
    private readonly BACnetClientService _bacnetClient;
    private readonly OutputService _outputService;
    private readonly ValueConverter _valueConverter;
    private readonly bool _runOnce;

    private int _consecutiveFailures = 0;
    private DateTime _lastSuccessfulPoll = DateTime.MinValue;
    private readonly object _backoffLock = new();

    public PollingService(
        ILogger<PollingService> logger,
        IOptions<AppConfiguration> appConfig,
        IOptions<DataPointConfiguration> dataConfig,
        BACnetClientService bacnetClient,
        OutputService outputService,
        ValueConverter valueConverter,
        bool runOnce = false)
    {
        _logger = logger;
        _appConfig = appConfig.Value;
        _dataConfig = dataConfig.Value;
        _bacnetClient = bacnetClient;
        _outputService = outputService;
        _valueConverter = valueConverter;
        _runOnce = runOnce;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting BACnet polling service...");
        
        // Initialize BACnet client
        if (!await _bacnetClient.InitializeAsync(stoppingToken))
        {
            _logger.LogError("Failed to initialize BACnet client. Exiting.");
            return;
        }

        _logger.LogInformation("BACnet client initialized. Starting polling loop...");
        _logger.LogInformation("Polling {DataPointCount} data points every {PeriodMs}ms", 
            _dataConfig.DataPoints.Count, _appConfig.Polling.PeriodMs);

        if (_runOnce)
        {
            await ExecuteSinglePollAsync(stoppingToken);
            return;
        }

        // Main polling loop
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var pollStartTime = DateTime.UtcNow;
                
                // Execute polling cycle
                var success = await ExecutePollingCycleAsync(stoppingToken);
                
                if (success)
                {
                    ResetBackoff();
                    _lastSuccessfulPoll = pollStartTime;
                }
                else
                {
                    await HandlePollingFailureAsync(stoppingToken);
                }

                // Calculate next poll time
                var nextPollDelay = CalculateNextPollDelay(pollStartTime);
                
                if (nextPollDelay > TimeSpan.Zero)
                {
                    _logger.LogDebug("Waiting {DelayMs}ms until next poll", nextPollDelay.TotalMilliseconds);
                    await Task.Delay(nextPollDelay, stoppingToken);
                }
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Polling service cancelled");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in polling loop");
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }

        _logger.LogInformation("BACnet polling service stopped");
    }

    /// <summary>
    /// Execute a single polling cycle
    /// </summary>
    private async Task<bool> ExecutePollingCycleAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Starting polling cycle for {DataPointCount} data points", _dataConfig.DataPoints.Count);
            
            // Read all data points
            var results = await _bacnetClient.ReadDataPointsAsync(_dataConfig.DataPoints, cancellationToken);
            
            // Convert values using the value converter
            var convertedResults = results.Select(result => ConvertResult(result)).ToList();
            
            // Output results
            await _outputService.OutputResultsAsync(convertedResults, cancellationToken);
            
            // Check if all reads failed
            var successfulReads = convertedResults.Count(r => r.Quality == "good");
            var totalReads = convertedResults.Count;
            
            _logger.LogDebug("Polling cycle completed: {SuccessfulReads}/{TotalReads} successful", 
                successfulReads, totalReads);
            
            // Consider it a failure if all reads failed
            if (successfulReads == 0 && totalReads > 0)
            {
                _logger.LogWarning("All data point reads failed in this cycle");
                return false;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during polling cycle");
            return false;
        }
    }

    /// <summary>
    /// Execute a single poll and exit (for --once mode)
    /// </summary>
    private async Task ExecuteSinglePollAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing single poll cycle...");
        
        var success = await ExecutePollingCycleAsync(cancellationToken);
        
        if (success)
        {
            _logger.LogInformation("Single poll completed successfully");
        }
        else
        {
            _logger.LogWarning("Single poll completed with errors");
        }
    }

    /// <summary>
    /// Convert BACnet result using the value converter
    /// </summary>
    private BACnetReadResult ConvertResult(BACnetReadResult result)
    {
        if (result.Quality != "good" || result.Value == null)
        {
            return result; // Don't convert failed reads
        }

        try
        {
            // Find the original data point to get scale information
            var dataPoint = _dataConfig.DataPoints.FirstOrDefault(dp => dp.Name == result.Name);
            var convertedValue = _valueConverter.ConvertValue(result.Value, result.ObjectType, dataPoint?.Scale);
            
            return result with { Value = convertedValue };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert value for data point {DataPoint}", result.Name);
            return result; // Return original result if conversion fails
        }
    }

    /// <summary>
    /// Handle polling failure with exponential backoff
    /// </summary>
    private async Task HandlePollingFailureAsync(CancellationToken cancellationToken)
    {
        lock (_backoffLock)
        {
            _consecutiveFailures++;
        }

        _logger.LogWarning("Polling cycle failed. Consecutive failures: {ConsecutiveFailures}", _consecutiveFailures);

        // If we've had 3 consecutive failures, try to reconnect
        if (_consecutiveFailures >= 3)
        {
            _logger.LogWarning("Multiple consecutive failures detected. Attempting to reconnect...");
            
            try
            {
                var reconnected = await _bacnetClient.ReconnectAsync(cancellationToken);
                if (reconnected)
                {
                    _logger.LogInformation("BACnet client reconnected successfully");
                }
                else
                {
                    _logger.LogError("Failed to reconnect BACnet client");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during BACnet client reconnection");
            }
        }

        // Apply exponential backoff
        var backoffDelay = CalculateBackoffDelay();
        if (backoffDelay > TimeSpan.Zero)
        {
            _logger.LogInformation("Applying backoff delay: {BackoffSeconds}s", backoffDelay.TotalSeconds);
            await Task.Delay(backoffDelay, cancellationToken);
        }
    }

    /// <summary>
    /// Calculate exponential backoff delay
    /// </summary>
    private TimeSpan CalculateBackoffDelay()
    {
        if (_consecutiveFailures <= 1)
        {
            return TimeSpan.Zero;
        }

        // Exponential backoff: 2^(failures-1) seconds, capped at MaxBackoffSeconds
        var backoffSeconds = Math.Min(
            Math.Pow(2, _consecutiveFailures - 1),
            _appConfig.Polling.MaxBackoffSeconds);

        return TimeSpan.FromSeconds(backoffSeconds);
    }

    /// <summary>
    /// Reset backoff state after successful poll
    /// </summary>
    private void ResetBackoff()
    {
        lock (_backoffLock)
        {
            if (_consecutiveFailures > 0)
            {
                _logger.LogInformation("Polling recovered after {ConsecutiveFailures} consecutive failures", _consecutiveFailures);
                _consecutiveFailures = 0;
            }
        }
    }

    /// <summary>
    /// Calculate delay until next poll
    /// </summary>
    private TimeSpan CalculateNextPollDelay(DateTime pollStartTime)
    {
        var elapsed = DateTime.UtcNow - pollStartTime;
        var targetInterval = TimeSpan.FromMilliseconds(_appConfig.Polling.PeriodMs);
        
        return targetInterval - elapsed;
    }

    /// <summary>
    /// Get polling statistics
    /// </summary>
    public PollingStatistics GetStatistics()
    {
        lock (_backoffLock)
        {
            return new PollingStatistics
            {
                ConsecutiveFailures = _consecutiveFailures,
                LastSuccessfulPoll = _lastSuccessfulPoll,
                IsInBackoff = _consecutiveFailures > 1,
                CurrentBackoffDelay = CalculateBackoffDelay(),
                ConfiguredDataPoints = _dataConfig.DataPoints.Count,
                PollingPeriodMs = _appConfig.Polling.PeriodMs
            };
        }
    }
}

/// <summary>
/// Statistics about the polling service
/// </summary>
public record PollingStatistics
{
    public required int ConsecutiveFailures { get; init; }
    public required DateTime LastSuccessfulPoll { get; init; }
    public required bool IsInBackoff { get; init; }
    public required TimeSpan CurrentBackoffDelay { get; init; }
    public required int ConfiguredDataPoints { get; init; }
    public required int PollingPeriodMs { get; init; }
}
