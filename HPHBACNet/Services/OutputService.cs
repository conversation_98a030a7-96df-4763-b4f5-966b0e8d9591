using System.Text.Json;
using HPHBACNet.Models;
using Microsoft.Extensions.Logging;

namespace HPHBACNet.Services;

/// <summary>
/// Service for outputting BACnet read results to console and JSON Lines files
/// </summary>
public class OutputService : IDisposable
{
    private readonly ILogger<OutputService> _logger;
    private readonly string _outputDirectory;
    private readonly SemaphoreSlim _fileSemaphore = new(1, 1);
    private string? _currentFilePath;
    private DateTime _currentFileDate;
    private bool _disposed;

    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    public OutputService(ILogger<OutputService> logger)
    {
        _logger = logger;
        _outputDirectory = Path.Combine(Directory.GetCurrentDirectory(), "out");
        
        // Ensure output directory exists
        Directory.CreateDirectory(_outputDirectory);
    }

    /// <summary>
    /// Output results to both console and JSON Lines file
    /// </summary>
    public async Task OutputResultsAsync(IEnumerable<BACnetReadResult> results, CancellationToken cancellationToken = default)
    {
        var resultsList = results.ToList();
        
        // Output to console
        OutputToConsole(resultsList);
        
        // Output to JSON Lines file
        await OutputToJsonFileAsync(resultsList, cancellationToken);
    }

    /// <summary>
    /// Output results to console (STDOUT)
    /// </summary>
    private void OutputToConsole(IList<BACnetReadResult> results)
    {
        foreach (var result in results)
        {
            var timestamp = result.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var valueStr = FormatValueForConsole(result.Value);
            var qualityIndicator = result.Quality == "good" ? "✓" : "✗";
            
            if (result.Quality == "good")
            {
                Console.WriteLine($"{timestamp} {qualityIndicator} {result.Name} = {valueStr} {result.Unit ?? ""}".Trim());
            }
            else
            {
                Console.WriteLine($"{timestamp} {qualityIndicator} {result.Name} = ERROR: {result.Error ?? "Unknown error"}");
            }
        }
    }

    /// <summary>
    /// Output results to JSON Lines file with daily rotation
    /// </summary>
    private async Task OutputToJsonFileAsync(IList<BACnetReadResult> results, CancellationToken cancellationToken)
    {
        await _fileSemaphore.WaitAsync(cancellationToken);
        try
        {
            var today = DateTime.Today;
            
            // Check if we need to rotate to a new file
            if (_currentFilePath == null || _currentFileDate != today)
            {
                _currentFileDate = today;
                _currentFilePath = Path.Combine(_outputDirectory, $"bacnet-read-{today:yyyyMMdd}.ndjson");
                
                _logger.LogInformation("Writing to JSON file: {FilePath}", _currentFilePath);
            }

            // Convert results to JSON output records
            var jsonRecords = results.Select(ConvertToJsonRecord).ToList();
            
            // Write to file
            var jsonLines = jsonRecords.Select(record => JsonSerializer.Serialize(record, JsonOptions));
            await File.AppendAllLinesAsync(_currentFilePath, jsonLines, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to write results to JSON file");
        }
        finally
        {
            _fileSemaphore.Release();
        }
    }

    /// <summary>
    /// Convert BACnetReadResult to JsonOutputRecord
    /// </summary>
    private static JsonOutputRecord ConvertToJsonRecord(BACnetReadResult result)
    {
        return new JsonOutputRecord
        {
            Ts = result.Timestamp.ToString("O"), // ISO 8601 format
            Name = result.Name,
            Type = result.ObjectType,
            Instance = result.Instance,
            Value = result.Value,
            Unit = result.Unit,
            Quality = result.Quality,
            ElapsedMs = result.ElapsedMs
        };
    }

    /// <summary>
    /// Format value for console output
    /// </summary>
    private static string FormatValueForConsole(object? value)
    {
        return value switch
        {
            null => "null",
            bool boolValue => boolValue ? "true" : "false",
            double doubleValue => doubleValue.ToString("F3"),
            float floatValue => floatValue.ToString("F3"),
            decimal decimalValue => decimalValue.ToString("F3"),
            int or long or uint or ulong => value.ToString()!,
            string stringValue => $"\"{stringValue}\"",
            _ => value.ToString() ?? "null"
        };
    }

    /// <summary>
    /// Get statistics about the current output session
    /// </summary>
    public async Task<OutputStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        await _fileSemaphore.WaitAsync(cancellationToken);
        try
        {
            var stats = new OutputStatistics
            {
                OutputDirectory = _outputDirectory,
                CurrentFilePath = _currentFilePath,
                CurrentFileDate = _currentFileDate
            };

            if (_currentFilePath != null && File.Exists(_currentFilePath))
            {
                var fileInfo = new FileInfo(_currentFilePath);
                stats.CurrentFileSize = fileInfo.Length;
                stats.CurrentFileLineCount = await CountLinesAsync(_currentFilePath, cancellationToken);
            }

            // Get all JSON files in output directory
            var jsonFiles = Directory.GetFiles(_outputDirectory, "bacnet-read-*.ndjson")
                .OrderBy(f => f)
                .ToList();
            
            stats.TotalFiles = jsonFiles.Count;
            stats.TotalSize = jsonFiles.Sum(f => new FileInfo(f).Length);

            return stats;
        }
        finally
        {
            _fileSemaphore.Release();
        }
    }

    /// <summary>
    /// Count lines in a file efficiently
    /// </summary>
    private static async Task<int> CountLinesAsync(string filePath, CancellationToken cancellationToken)
    {
        try
        {
            var lines = await File.ReadAllLinesAsync(filePath, cancellationToken);
            return lines.Length;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// Clean up old output files (keep only last N days)
    /// </summary>
    public async Task CleanupOldFilesAsync(int keepDays = 30, CancellationToken cancellationToken = default)
    {
        await _fileSemaphore.WaitAsync(cancellationToken);
        try
        {
            var cutoffDate = DateTime.Today.AddDays(-keepDays);
            var jsonFiles = Directory.GetFiles(_outputDirectory, "bacnet-read-*.ndjson");
            
            foreach (var file in jsonFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("bacnet-read-") && fileName.Length >= 20) // bacnet-read-YYYYMMDD
                {
                    var dateStr = fileName.Substring(12, 8); // Extract YYYYMMDD
                    if (DateTime.TryParseExact(dateStr, "yyyyMMdd", null, 
                        System.Globalization.DateTimeStyles.None, out var fileDate))
                    {
                        if (fileDate < cutoffDate)
                        {
                            try
                            {
                                File.Delete(file);
                                _logger.LogInformation("Deleted old output file: {FileName}", Path.GetFileName(file));
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to delete old output file: {FileName}", Path.GetFileName(file));
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old output files");
        }
        finally
        {
            _fileSemaphore.Release();
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _fileSemaphore.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Statistics about output files
/// </summary>
public record OutputStatistics
{
    public required string OutputDirectory { get; init; }
    public string? CurrentFilePath { get; init; }
    public DateTime CurrentFileDate { get; init; }
    public long CurrentFileSize { get; set; }
    public int CurrentFileLineCount { get; set; }
    public int TotalFiles { get; set; }
    public long TotalSize { get; set; }
}
