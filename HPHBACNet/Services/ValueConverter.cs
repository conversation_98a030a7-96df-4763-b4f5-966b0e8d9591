using System.IO.BACnet;
using Microsoft.Extensions.Logging;

namespace HPHBACNet.Services;

/// <summary>
/// Service for converting BACnet values to appropriate .NET types
/// </summary>
public class ValueConverter
{
    private readonly ILogger<ValueConverter> _logger;

    public ValueConverter(ILogger<ValueConverter> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Convert a BACnet value to the appropriate .NET type based on the object type
    /// </summary>
    public object? ConvertValue(object? bacnetValue, string objectType, double? scale = null)
    {
        if (bacnetValue == null)
        {
            return null;
        }

        try
        {
            return objectType.ToLowerInvariant() switch
            {
                "analoginput" or "analogoutput" or "analogvalue" => ConvertAnalogValue(bacnetValue, scale),
                "binaryinput" or "binaryoutput" or "binaryvalue" => ConvertBinaryValue(bacnetValue),
                "multistateinput" or "multistateoutput" or "multistatevalue" => ConvertMultiStateValue(bacnetValue, scale),
                _ => ConvertGenericValue(bacnetValue, scale)
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert BACnet value {Value} for object type {ObjectType}", 
                bacnetValue, objectType);
            return bacnetValue.ToString();
        }
    }

    /// <summary>
    /// Convert analog values (typically Real/Double)
    /// </summary>
    private object? ConvertAnalogValue(object bacnetValue, double? scale)
    {
        return bacnetValue switch
        {
            float floatValue => scale.HasValue ? floatValue * scale.Value : (double)floatValue,
            double doubleValue => scale.HasValue ? doubleValue * scale.Value : doubleValue,
            int intValue => scale.HasValue ? intValue * scale.Value : (double)intValue,
            uint uintValue => scale.HasValue ? uintValue * scale.Value : (double)uintValue,
            long longValue => scale.HasValue ? longValue * scale.Value : (double)longValue,
            ulong ulongValue => scale.HasValue ? ulongValue * scale.Value : (double)ulongValue,
            decimal decimalValue => scale.HasValue ? (double)(decimalValue * (decimal)scale.Value) : (double)decimalValue,
            string stringValue when double.TryParse(stringValue, out var parsed) => 
                scale.HasValue ? parsed * scale.Value : parsed,
            _ => ConvertToDouble(bacnetValue, scale)
        };
    }

    /// <summary>
    /// Convert binary values (typically Boolean)
    /// </summary>
    private object? ConvertBinaryValue(object bacnetValue)
    {
        return bacnetValue switch
        {
            bool boolValue => boolValue,
            BacnetBitString bitString => ConvertBitStringToBool(bitString),
            int intValue => intValue != 0,
            uint uintValue => uintValue != 0,
            float floatValue => Math.Abs(floatValue) > float.Epsilon,
            double doubleValue => Math.Abs(doubleValue) > double.Epsilon,
            string stringValue => ConvertStringToBool(stringValue),
            _ => bacnetValue.ToString()?.ToLowerInvariant() switch
            {
                "true" or "1" or "on" or "active" or "yes" => true,
                "false" or "0" or "off" or "inactive" or "no" => false,
                _ => null
            }
        };
    }

    /// <summary>
    /// Convert multi-state values (typically Enumerated/Integer)
    /// </summary>
    private object? ConvertMultiStateValue(object bacnetValue, double? scale)
    {
        return bacnetValue switch
        {
            int intValue => scale.HasValue ? (object)(intValue * scale.Value) : intValue,
            uint uintValue => scale.HasValue ? (object)(uintValue * scale.Value) : (long)uintValue,
            long longValue => scale.HasValue ? (object)(longValue * scale.Value) : longValue,
            ulong ulongValue => scale.HasValue ? (object)(ulongValue * scale.Value) : (double)ulongValue,
            float floatValue => scale.HasValue ? (object)(floatValue * scale.Value) : (double)floatValue,
            double doubleValue => scale.HasValue ? (object)(doubleValue * scale.Value) : doubleValue,
            BacnetBitString bitString => ConvertBitStringToInt(bitString, scale),
            string stringValue when int.TryParse(stringValue, out var parsed) =>
                scale.HasValue ? (object)(parsed * scale.Value) : parsed,
            _ => ConvertToInt(bacnetValue, scale)
        };
    }

    /// <summary>
    /// Convert generic values when object type is unknown
    /// </summary>
    private object? ConvertGenericValue(object bacnetValue, double? scale)
    {
        return bacnetValue switch
        {
            bool boolValue => boolValue,
            float floatValue => scale.HasValue ? floatValue * scale.Value : (double)floatValue,
            double doubleValue => scale.HasValue ? doubleValue * scale.Value : doubleValue,
            int intValue => scale.HasValue ? intValue * scale.Value : intValue,
            uint uintValue => scale.HasValue ? uintValue * scale.Value : (long)uintValue,
            long longValue => scale.HasValue ? longValue * scale.Value : longValue,
            ulong ulongValue => scale.HasValue ? ulongValue * scale.Value : (double)ulongValue,
            decimal decimalValue => scale.HasValue ? (double)(decimalValue * (decimal)scale.Value) : (double)decimalValue,
            BacnetBitString bitString => ConvertBitStringToInt(bitString, scale),
            BacnetObjectId objectId => objectId.ToString(),
            DateTime dateTime => dateTime.ToString("O"), // ISO 8601 format
            DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("O"),
            _ => bacnetValue.ToString()
        };
    }

    /// <summary>
    /// Convert BACnet bit string to boolean
    /// </summary>
    private bool ConvertBitStringToBool(BacnetBitString bitString)
    {
        try
        {
            // Check if any bit is set - use Length property instead of BitsUsed
            for (int i = 0; i < bitString.Length; i++)
            {
                if (bitString.GetBit((byte)i))
                {
                    return true;
                }
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Convert BACnet bit string to integer
    /// </summary>
    private object ConvertBitStringToInt(BacnetBitString bitString, double? scale)
    {
        try
        {
            // Use a simple conversion approach since ConvertToInt may not be available
            var intValue = bitString.GetHashCode(); // Fallback approach
            return scale.HasValue ? intValue * scale.Value : intValue;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// Convert string to boolean
    /// </summary>
    private bool? ConvertStringToBool(string stringValue)
    {
        return stringValue.ToLowerInvariant() switch
        {
            "true" or "1" or "on" or "active" or "yes" or "enabled" => true,
            "false" or "0" or "off" or "inactive" or "no" or "disabled" => false,
            _ => null
        };
    }

    /// <summary>
    /// Attempt to convert any value to double
    /// </summary>
    private double? ConvertToDouble(object value, double? scale)
    {
        try
        {
            var doubleValue = Convert.ToDouble(value);
            return scale.HasValue ? doubleValue * scale.Value : doubleValue;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Attempt to convert any value to integer
    /// </summary>
    private object? ConvertToInt(object value, double? scale)
    {
        try
        {
            if (value is string stringValue && int.TryParse(stringValue, out var intValue))
            {
                return scale.HasValue ? intValue * scale.Value : intValue;
            }

            var convertedValue = Convert.ToInt64(value);
            return scale.HasValue ? convertedValue * scale.Value : convertedValue;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Get a human-readable description of the converted value type
    /// </summary>
    public string GetValueTypeDescription(object? value)
    {
        return value switch
        {
            null => "null",
            bool => "boolean",
            int or long or uint or ulong => "integer",
            float or double or decimal => "number",
            string => "string",
            DateTime or DateTimeOffset => "datetime",
            _ => value.GetType().Name.ToLowerInvariant()
        };
    }
}
